name: Deploy with Serverless
run-name: ${{ github.actor }} trigger serverless deployment 🚀

on:
   push:
      branches:
         - v2.1

jobs:
   deploy:
      runs-on: ubuntu-latest
      steps:
         -  name: Checkout repository
            uses: actions/checkout@v4

         -  name: Setup Node.js
            uses: actions/setup-node@v4
            with:
               node-version: '22'  # or your project's version

         -  name: Install dependencies
            run: npm ci

         -  name: build
            run: npm run build:lambda

         -  name: Configure AWS credentials
            uses: aws-actions/configure-aws-credentials@v2
            with:
               aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
               aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
               aws-region: eu-west-1

         -  name: Deploy with Serverless
            env:
               SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
               # or if using AWS credentials directly
               AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
               AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            run: npm run deploy

         -  name: Clear cache after deployment
            run: |
               curl --location 'https://staging.action.jo/api/cache/clear' \
               --header 'Content-Type: application/json' \
               --header 'x-nuxt-multi-cache-token: ${{ secrets.NUXT_CACHE_TOKEN }}' \
               --header 'Cookie: i18n_redirected=ar; visitorId=1750595382173' \
               --data '[
                   "home",
                   "product",
                   "search"
               ]'
